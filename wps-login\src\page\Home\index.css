/* 主容器 */
.wrap {
  display: flex;
  min-height: 100vh;
  background-color: #ffffff;
  align-items: center;
  justify-content: space-between;
  padding: 40px 100px;
  max-width: 1400px;
  margin: 0 auto;
  gap: 80px;
}

/* 左侧区域 */
.left_section {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #ffffff;
}

.logo_wrap {
  display: flex;
  justify-content: center;
  align-items: center;
}

.logo_image {
  max-width: 400px;
  max-height: 500px;
  width: auto;
  height: auto;
  object-fit: contain;
}

/* 右侧区域 */
.right_section {
  width: 400px;
  flex-shrink: 0;
  background-color: #fff;
  padding: 32px;
  border: 1px solid #e7e9eb;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  width: 400px;
  height: 497.7px;
}

.login_container {
  width: 100%;
  max-width: 400px;
  text-align: center;
}

/* 弹窗样式 */
.modal_overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal_content {
  background: white;
  border-radius: 8px;
  width: 400px;
  max-width: 90vw;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.modal_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.modal_title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.modal_close {
  background: none;
  border: none;
  font-size: 24px;
  color: #999;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;
}

.modal_close:hover {
  background-color: #f5f5f5;
  color: #666;
}

.modal_body {
  padding: 20px 24px;
}

.modal_body p {
  margin: 0;
  line-height: 1.6;
  color: #666;
  font-size: 14px;
}

.modal_footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px 20px;
  border-top: 1px solid #f0f0f0;
}

.modal_btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
  min-width: 64px;
}

.cancel_btn {
  background: white;
  color: #666;
  border-color: #d9d9d9;
}

.cancel_btn:hover {
  background: #f5f5f5;
  border-color: #b3b3b3;
}

.confirm_btn {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.confirm_btn:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 移动端样式 - 小于400px */
@media (max-width: 640px) {
  .wrap {
    flex-direction: column;
    padding: 0;
    gap: 0;
    min-height: 100vh;
    background-color: #ffffff;
  }

  .left_section {
    width: 100%;
    padding: 20px 10px 15px;
    background-color: #ffffff;
    order: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
  }

  .right_section {
    width: 100%;
    flex: 1;
    padding: 0;
    background-color: #ffffff;
    border: none;
    border-radius: 0;
    box-shadow: none;
    order: 2;
    height: auto;
  }

  .login_container {
    max-width: 100%;
    background-color: #ffffff;
    border-radius: 0;
    padding: 0 24px 24px;
    box-shadow: none;
    border: none;
    margin-top: -70px;
  }

  .logo_image {
    width: 350px;
    height: auto;
    object-fit: contain;
    margin: 0 auto 10px;
  }
}