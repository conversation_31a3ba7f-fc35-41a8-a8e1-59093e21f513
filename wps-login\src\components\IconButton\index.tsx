import React from "react";
import { useMobile } from "../../hooks/useMobile";
import "./index.css";

export interface IconButtonProps {
  /** 按钮标识符 */
  id: string;
  /** 显示文本 */
  text: string;
  /** 点击事件处理 */
  onClick: (id: string) => void;
  /** 自定义CSS类名 */
  className?: string;
  /** 移动端图标配置 */
  mobileIcon?: {
    type: "svg" | "image" | "text";
    content: React.ReactNode | string;
    size?: number;
  };
  /** PC端图标配置 */
  desktopIcon?: {
    type: "svg" | "image" | "text";
    content: React.ReactNode | string;
    size?: number;
  };
  /** 是否禁用 */
  disabled?: boolean;
}

const IconButton: React.FC<IconButtonProps> = ({
  id,
  text,
  onClick,
  className = "",
  mobileIcon,
  desktopIcon,
  disabled = false
}) => {
  const isMobile = useMobile();

  // 根据设备类型选择图标配置
  const currentIcon = isMobile ? mobileIcon : desktopIcon;

  const handleClick = () => {
    if (!disabled) {
      onClick(id);
    }
  };

  const renderIcon = () => {
    if (!currentIcon) return null;

    const { type, content, size = 27 } = currentIcon;

    switch (type) {
      case "svg":
        return (
          <div
            className="icon-button__icon"
            style={{ width: size, height: size }}
          >
            {content}
          </div>
        );
      case "image":
        return (
          <img
            className="icon-button__icon"
            src={content as string}
            alt={text}
            style={{ width: size, height: size }}
          />
        );
      case "text":
        return (
          <div
            className="icon-button__icon icon-button__icon--text"
            style={{ fontSize: size }}
          >
            {content}
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div
      className={`icon-button ${className} ${disabled ? "icon-button--disabled" : ""}`}
      onClick={handleClick}
    >
      {renderIcon()}
      <span className="icon-button__text">{text}</span>
    </div>
  );
};

export default IconButton;
