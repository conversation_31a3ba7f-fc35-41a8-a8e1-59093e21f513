import wxLogo from "../../assets/wxlogo.png";
import Common from "../../components/Common";
import { type LoginMethod } from "../../constants";
import "./index.css";

interface QRCodeLoginProps {
  onCurrentBtn: (value: LoginMethod) => void;
}

function QRCodeLogin({ onCurrentBtn }: QRCodeLoginProps) {
  const handleCurrentBtn = (value: LoginMethod) => {
    console.log("handleCurrentBtn被调用", value);
    onCurrentBtn(value);
  };

  return (
    <Common
      isShowBack={false}
      title="微信扫码登录"
      subTitle="使用金山办公在线服务账号登录"
      fchildren={
        <div className="login_options">
          <div className="checkbox-group">
            <div className="checkbox-div">
              <label className="checkbox-item">
                <input type="checkbox" />
                <span>自动登录</span>
              </label>
            </div>
            <div className="checkbox-div">
              <label className="checkbox-item">
                <input type="checkbox" />
                <span>
                  已阅读并同意
                  <a href="#">隐私保护政策</a>和<a href="#">在线服务协议</a>
                </span>
              </label>
            </div>
          </div>

          <div className="login_methods">
            <div
              className="login_method"
              onClick={() => handleCurrentBtn("qq")}
            >
              <div className="method_icon qq_icon">
                <svg
                  className="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="2768"
                  id="mx_n_1753146181203"
                  width="32"
                  height="32"
                >
                  <path
                    d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m210.5 612.4c-11.5 1.4-44.9-52.7-44.9-52.7 0 31.3-16.2 72.2-51.1 101.8 16.9 5.2 54.9 19.2 45.9 34.4-7.3 12.3-125.6 7.9-159.8 4-34.2 3.8-152.5 8.3-159.8-4-9.1-15.2 28.9-29.2 45.8-34.4-35-29.5-51.1-70.4-51.1-101.8 0 0-33.4 54.1-44.9 52.7-5.4-0.7-12.4-29.6 9.4-99.7 10.3-33 22-60.5 40.2-105.8-3.1-116.9 45.3-215 160.4-215 113.9 0 163.3 96.1 160.4 215 18.1 45.2 29.9 72.8 40.2 105.8 21.7 70.1 14.6 99.1 9.3 99.7z"
                    p-id="2769"
                    fill="#1296db"
                  ></path>
                </svg>
              </div>
              <span className="method_text">QQ账号</span>
            </div>
            <div
              className="login_method"
              onClick={() => handleCurrentBtn("phone")}
            >
              <div className="method_icon phone_icon">
                <svg
                  className="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="51375"
                  width="32"
                  height="32"
                >
                  <path
                    d="M512 0c282.771911 0 512 229.228089 512 512s-229.228089 512-512 512S0 794.771911 0 512 229.228089 0 512 0z m111.189333 204.714667h-222.378666c-11.275378 0-21.822578 2.133333-31.635911 6.405689a81.095111 81.095111 0 0 0-25.685334 17.544533 82.147556 82.147556 0 0 0-17.225955 25.969778c-4.175644 9.898667-6.263467 20.462933-6.263467 31.704177v451.9936c0 11.246933 2.087822 21.703111 6.263467 31.368534a83.228444 83.228444 0 0 0 17.225955 25.634133 81.095111 81.095111 0 0 0 25.685334 17.544533c9.813333 4.266667 20.360533 6.405689 31.630222 6.405689h222.384355c11.275378 0 21.822578-2.133333 31.635911-6.405689a81.095111 81.095111 0 0 0 25.685334-17.544533 83.228444 83.228444 0 0 0 17.225955-25.634133c4.175644-9.671111 6.263467-20.1216 6.263467-31.368534V286.338844c0-11.241244-2.087822-21.8112-6.263467-31.704177a82.147556 82.147556 0 0 0-17.225955-25.969778 81.095111 81.095111 0 0 0-25.685334-17.544533c-9.813333-4.266667-20.360533-6.405689-31.630222-6.405689z m-111.502222 519.452444c9.187556 0 16.913067 3.373511 23.176533 10.120533 6.263467 6.741333 9.398044 15.064178 9.398045 24.957156 0 9.443556-3.128889 17.652622-9.398045 24.6272-6.263467 6.968889-13.988978 10.456178-23.176533 10.456178-8.772267 0-16.389689-3.487289-22.869333-10.456178-6.468267-6.974578-9.705244-15.183644-9.705245-24.6272 0-9.892978 3.236978-18.215822 9.710934-24.957156 6.473956-6.747022 14.091378-10.126222 22.863644-10.126222z m141.573689-405.447111v386.56H370.113422v-386.56h283.147378zM551.776711 249.912889c2.503111 0 4.801422 1.012622 6.894933 3.037867 2.087822 2.019556 3.128889 4.835556 3.128889 8.430933s-1.041067 6.405689-3.128889 8.430933c-2.093511 2.025244-4.386133 3.037867-6.894933 3.037867H472.223289c-2.503111 0-4.801422-1.012622-6.894933-3.037867-2.087822-2.025244-3.128889-4.835556-3.128889-8.430933 0-3.601067 1.149156-6.411378 3.447466-8.430933 2.292622-2.025244 4.488533-3.037867 6.576356-3.037867z"
                    fill="#11a7fa"
                    p-id="51376"
                  ></path>
                </svg>
              </div>
              <span className="method_text">手机</span>
            </div>
            <div
              className="login_method"
              onClick={() => handleCurrentBtn("sso")}
            >
              <div className="method_icon sso_icon">
                <svg
                  className="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="21242"
                  width="27"
                  height="27"
                  style={{ margin: "4px" }}
                >
                  <path
                    d="M313.344 569.2672c0 19.968-5.1456 37.888-15.4368 53.76-10.2656 15.9232-25.3184 28.3648-45.1072 37.376-19.7888 8.9856-43.264 13.4912-70.4256 13.4912-32.5376 0-59.392-6.144-80.5376-18.432a107.3152 107.3152 0 0 1-36.5824-35.456C55.8848 605.2352 51.2 590.8736 51.2 576.9216c0-8.0896 2.816-15.0272 8.448-20.8128 5.632-5.76 12.8-8.6528 21.4784-8.6528 7.04 0 13.0048 2.2528 17.8944 6.7328 4.864 4.5056 9.0368 11.1872 12.4672 20.0448 4.224 10.496 8.7552 19.2768 13.6192 26.3168s11.7504 12.8512 20.5824 17.4336c8.8576 4.5824 20.48 6.8608 34.8928 6.8608 19.7888 0 35.8912-4.608 48.256-13.824 12.3648-9.216 18.56-20.736 18.56-34.56 0-10.9312-3.328-19.8144-10.0096-26.624a64.5632 64.5632 0 0 0-25.856-15.6672c-10.5984-3.584-24.7296-7.424-42.4448-11.4688-23.68-5.5552-43.52-12.032-59.4944-19.456-15.9744-7.424-28.672-17.5616-38.0416-30.3872-9.3696-12.8-14.0544-28.7488-14.0544-47.7952 0-18.176 4.9408-34.304 14.848-48.384 9.9072-14.08 24.2176-24.9344 42.9824-32.512 18.7392-7.5776 40.8064-11.3664 66.1504-11.3664 20.224 0 37.76 2.5088 52.5312 7.5264 14.7712 5.0432 27.0336 11.6992 36.7872 20.0448 9.7536 8.32 16.896 17.0496 21.376 26.2144 4.5056 9.1392 6.7584 18.0736 6.7584 26.752 0 7.9616-2.816 15.104-8.448 21.504-5.632 6.3744-12.6208 9.5488-21.0432 9.5488-7.6288 0-13.44-1.8944-17.408-5.7344-3.9936-3.84-8.2944-10.0864-12.9536-18.7904-6.016-12.4416-13.2096-22.144-21.6064-29.1328-8.3968-6.9632-21.888-10.4704-40.4992-10.4704-17.2544 0-31.1552 3.7888-41.728 11.3664-10.5728 7.5776-15.872 16.6912-15.872 27.3408 0 6.6048 1.792 12.288 5.4016 17.1008 3.584 4.8128 8.5504 8.9344 14.848 12.3904 6.2976 3.4304 12.672 6.144 19.1232 8.0896 6.4512 1.9456 17.1008 4.7872 31.9488 8.5504 18.6112 4.352 35.4304 9.1392 50.5088 14.3872 15.104 5.2736 27.904 11.648 38.4768 19.1488 10.5728 7.5008 18.8416 16.9728 24.7552 28.4416 5.9392 11.4944 8.8832 25.5488 8.8832 42.1888z m307.328 0c0 19.968-5.12 37.888-15.4112 53.76-10.2656 15.9232-25.3184 28.3648-45.1072 37.376-19.8144 8.9856-43.264 13.4912-70.4256 13.4912-32.5632 0-59.392-6.144-80.5632-18.432a107.3152 107.3152 0 0 1-36.5568-35.456c-9.3696-14.7712-14.08-29.1328-14.08-43.0848 0-8.0896 2.816-15.0272 8.448-20.8128 5.632-5.76 12.8-8.6528 21.504-8.6528 7.04 0 13.0048 2.2528 17.8944 6.7328 4.864 4.5056 9.0368 11.1872 12.4672 20.0448 4.1984 10.496 8.7552 19.2768 13.6192 26.3168s11.7504 12.8512 20.5824 17.4336c8.8576 4.5824 20.48 6.8608 34.8928 6.8608 19.7888 0 35.8656-4.608 48.256-13.824 12.3648-9.216 18.56-20.736 18.56-34.56 0-10.9312-3.328-19.8144-10.0096-26.624a64.5632 64.5632 0 0 0-25.856-15.6672c-10.5984-3.584-24.7296-7.424-42.4448-11.4688-23.68-5.5552-43.52-12.032-59.4944-19.456-15.9744-7.424-28.672-17.5616-38.0416-30.3872-9.3696-12.8-14.0544-28.7488-14.0544-47.7952 0-18.176 4.9408-34.304 14.848-48.384 9.9072-14.08 24.2176-24.9344 42.9824-32.512 18.7392-7.5776 40.7808-11.3664 66.1504-11.3664 20.224 0 37.76 2.5088 52.5312 7.5264 14.7712 5.0432 27.0336 11.6992 36.7872 20.0448 9.728 8.32 16.896 17.0496 21.376 26.2144 4.5056 9.1392 6.7584 18.0736 6.7584 26.752 0 7.9616-2.816 15.104-8.448 21.504-5.632 6.3744-12.6464 9.5488-21.0432 9.5488-7.6544 0-13.4656-1.8944-17.408-5.7344-3.9936-3.84-8.32-10.0864-12.9536-18.7904-6.016-12.4416-13.2096-22.144-21.6064-29.1328-8.3968-6.9632-21.888-10.4704-40.4992-10.4704-17.2544 0-31.1552 3.7888-41.728 11.3664-10.5984 7.5776-15.872 16.6912-15.872 27.3408 0 6.6048 1.792 12.288 5.376 17.1008 3.6352 4.8128 8.576 8.9344 14.8736 12.3904 6.2976 3.4304 12.672 6.144 19.1232 8.0896 6.4512 1.9456 17.1008 4.7872 31.9488 8.5504 18.6112 4.352 35.4304 9.1392 50.5088 14.3872 15.104 5.2736 27.904 11.648 38.4768 19.1488 10.5728 7.5008 18.8416 16.9728 24.7552 28.4416 5.9136 11.4944 8.8832 25.5488 8.8832 42.1888zM822.528 332.8c34.2016 0 63.5648 6.9376 88.064 20.8128a134.912 134.912 0 0 1 55.7056 59.1872c12.5952 25.5744 18.8928 55.6032 18.8928 90.112 0 25.4976-3.4304 48.64-10.3424 69.504a150.4256 150.4256 0 0 1-31.0528 54.2464 135.6544 135.6544 0 0 1-50.8416 35.072c-20.096 8.1152-43.136 12.16-69.0688 12.16-25.8048 0-48.896-4.1472-69.2992-12.4928a137.472 137.472 0 0 1-51.0976-35.2c-13.6448-15.1552-23.9616-33.3824-30.9248-54.6816a220.7232 220.7232 0 0 1-10.4704-69.0688c0-25.344 3.6352-48.5888 10.9312-69.76 7.2704-21.1456 17.792-39.1424 31.5904-53.9904a136.704 136.704 0 0 1 50.432-34.0992c19.7888-7.8592 42.2912-11.8016 67.4816-11.8016z m94.9504 169.6512c0-24.1408-3.8912-45.056-11.6992-62.7712-7.808-17.7152-18.944-31.104-33.408-40.1664-14.4896-9.088-31.104-13.6192-49.8432-13.6192-13.3632 0-25.7024 2.5088-37.0176 7.552a81.3312 81.3312 0 0 0-29.2352 21.9392c-8.192 9.6-14.6432 21.8624-19.3536 36.7872-4.736 14.9248-7.0912 31.6672-7.0912 50.2784 0 18.7392 2.3552 35.6864 7.0912 50.8416 4.7104 15.1552 11.392 27.7248 20.0192 37.6832 8.6272 9.984 18.5344 17.4592 29.696 22.4 11.1872 4.9408 23.4496 7.424 36.7872 7.424 17.1008 0 32.8192-4.2752 47.1296-12.8256 14.336-8.5504 25.728-21.76 34.2016-39.6032 8.4736-17.8432 12.7232-39.8336 12.7232-65.92z"
                    fill="#ffffff"
                    p-id="21243"
                  ></path>
                </svg>
              </div>
              <span className="method_text">专属账号</span>
            </div>
            <div
              className="login_method"
              onClick={() => handleCurrentBtn("more")}
            >
              <div className="method_icon more_icon">
                <svg
                  className="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="15715"
                  width="32"
                  height="32"
                >
                  <path
                    d="M512 0C229.218329 0 0 229.239892 0 512s229.218329 512 512 512 512-229.239892 512-512S794.760108 0 512 0z m-201.789757 569.638814a57.649596 57.649596 0 1 1 0-115.299191 57.768194 57.768194 0 0 1 57.638814 57.660377 57.832884 57.832884 0 0 1-57.638814 57.638814z m201.789757 0a57.638814 57.638814 0 1 1 57.681941-57.638814 57.832884 57.832884 0 0 1-57.681941 57.638814z m201.811321 0a57.638814 57.638814 0 1 1 57.638814-57.638814 57.832884 57.832884 0 0 1-57.660378 57.638814z"
                    fill="#707070"
                    p-id="15716"
                  ></path>
                </svg>
              </div>
              <span className="method_text">更多</span>
            </div>
          </div>
        </div>
      }
    >
      <div className="qr_code_section">
        <div className="qr_code_container">
          <img src={wxLogo} />
        </div>
      </div>
    </Common>
  );
}

export default QRCodeLogin;
