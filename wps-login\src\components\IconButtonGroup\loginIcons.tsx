import React from "react";
import { type IconButtonProps } from "../IconButton";

// 微信图标SVG
const WechatIcon = () => (
  <svg viewBox="0 0 1024 1024" width="100%" height="100%">
    <path
      d="M331.5 307.4c22.5 0 37.5 15 37.5 37.5s-15 37.5-37.5 37.5-37.5-15-37.5-37.5 15-37.5 37.5-37.5zM556.5 307.4c22.5 0 37.5 15 37.5 37.5s-15 37.5-37.5 37.5-37.5-15-37.5-37.5 15-37.5 37.5-37.5z"
      fill="#ffffff"
    />
    <path
      d="M331.5 270c-41.2 0-75 33.8-75 75s33.8 75 75 75 75-33.8 75-75-33.8-75-75-75z m0 112.5c-20.7 0-37.5-16.8-37.5-37.5s16.8-37.5 37.5-37.5 37.5 16.8 37.5 37.5-16.8 37.5-37.5 37.5zM556.5 270c-41.2 0-75 33.8-75 75s33.8 75 75 75 75-33.8 75-75-33.8-75-75-75z m0 112.5c-20.7 0-37.5-16.8-37.5-37.5s16.8-37.5 37.5-37.5 37.5 16.8 37.5 37.5-16.8 37.5-37.5 37.5z"
      fill="#07C160"
    />
  </svg>
);

// QQ图标SVG
const QQIcon = () => (
  <svg viewBox="0 0 1024 1024" width="100%" height="100%">
    <path
      d="M512 74.667C270.933 74.667 74.667 270.933 74.667 512S270.933 949.333 512 949.333 949.333 753.067 949.333 512 753.067 74.667 512 74.667z"
      fill="#12B7F5"
    />
  </svg>
);

// SSO图标SVG
const SSOIcon = () => (
  <svg viewBox="0 0 1024 1024" width="100%" height="100%">
    <path
      d="M313.344 569.2672c0 19.968-5.1456 37.888-15.4368 53.76-10.2656 15.9232-25.3184 28.3648-45.1072 37.376-19.7888 8.9856-43.264 13.4912-70.4256 13.4912-32.5376 0-59.392-6.144-80.5376-18.432a107.3152 107.3152 0 0 1-36.5824-35.456C55.8848 605.2352 51.2 590.8736 51.2 576.9216c0-8.0896 2.816-15.0272 8.448-20.8128 5.632-5.76 12.8-8.6528 21.4784-8.6528 7.04 0 13.0048 2.2528 17.8944 6.7328 4.864 4.5056 9.0368 11.1872 12.4672 20.0448 4.224 10.496 8.7552 19.2768 13.6192 26.3168s11.7504 12.8512 20.5824 17.4336c8.8576 4.5824 20.48 6.8608 34.8928 6.8608 19.7888 0 35.8912-4.608 48.256-13.824 12.3648-9.216 18.56-20.736 18.56-34.56 0-10.9312-3.328-19.8144-10.0096-26.624a64.5632 64.5632 0 0 0-25.856-15.6672c-10.5984-3.584-24.7296-7.424-42.4448-11.4688-23.68-5.5552-43.52-12.032-59.4944-19.456-15.9744-7.424-28.672-17.5616-38.0416-30.3872-9.3696-12.8-14.0544-28.7488-14.0544-47.7952 0-18.176 4.9408-34.304 14.848-48.384 9.9072-14.08 24.2176-24.9344 42.9824-32.512 18.7392-7.5776 40.8064-11.3664 66.1504-11.3664 20.224 0 37.76 2.5088 52.5312 7.5264 14.7712 5.0432 27.0336 11.6992 36.7872 20.0448 9.7536 8.32 16.896 17.0496 21.376 26.2144 4.5056 9.1392 6.7584 18.0736 6.7584 26.752 0 7.9616-2.816 15.104-8.448 21.504-5.632 6.3744-12.6208 9.5488-21.0432 9.5488-7.6288 0-13.44-1.8944-17.408-5.7344-3.9936-3.84-8.2944-10.0864-12.9536-18.7904-6.016-12.4416-13.2096-22.144-21.6064-29.1328-8.3968-6.9632-21.888-10.4704-40.4992-10.4704-17.2544 0-31.1552 3.7888-41.728 11.3664-10.5728 7.5776-15.872 16.6912-15.872 27.3408 0 6.6048 1.792 12.288 5.4016 17.1008 3.584 4.8128 8.5504 8.9344 14.848 12.3904 6.2976 3.4304 12.672 6.144 19.1232 8.0896 6.4512 1.9456 17.1008 4.7872 31.9488 8.5504 18.6112 4.352 35.4304 9.1392 50.5088 14.3872 15.104 5.2736 27.904 11.648 38.4768 19.1488 10.5728 7.5008 18.8416 16.9728 24.7552 28.4416 5.9392 11.4944 8.8832 25.5488 8.8832 42.1888z"
      fill="#6C5CE7"
    />
  </svg>
);

// 苹果图标
const AppleIcon = () => (
  <svg viewBox="0 0 1024 1024" width="100%" height="100%">
    <path
      d="M747.4 535.7c-.4-68.2 30.5-119.6 92.9-157.5-34.9-50-87.7-80.5-147.1-85.7-60.2-5.1-120.4 33.1-151.4 33.1-33.2 0-81.2-30.6-135.8-29.8-65.5.8-129.3 34.3-163.1 87.7-72.6 116-21.3 294.5 50.8 391.1 37.4 49.6 81.9 105.5 140.8 103.5 56.1-2.1 77.4-33.6 145.5-33.6 66.9 0 86.3 33.6 145.8 32.5 60.9-1.1 99.4-49.4 136.8-99.2 42.9-57.2 60.7-115.6 61.6-118.4-1.3-.6-118.6-45.4-119-179.6z"
      fill="#000000"
    />
    <path
      d="M639.8 199.4c31.1-36.9 52.1-88.1 46.4-139.4-44.9 1.8-99.4 30.1-131.4 68.1-28.9 34.4-54.3 89.4-47.5 142.2 50.2 3.9 101.3-25.5 132.5-70.9z"
      fill="#000000"
    />
  </svg>
);

// 更多图标
const MoreIcon = () => (
  <div style={{ 
    width: '100%', 
    height: '100%', 
    display: 'flex', 
    alignItems: 'center', 
    justifyContent: 'center',
    fontSize: '18px',
    color: '#999'
  }}>
    ···
  </div>
);

// 预定义的登录方式配置
export const loginMethodConfigs: Record<string, Omit<IconButtonProps, 'onClick'>> = {
  wechat: {
    id: "wechat",
    text: "微信账号",
    mobileIcon: {
      type: "svg",
      content: <WechatIcon />,
      size: 40,
    },
    desktopIcon: {
      type: "svg", 
      content: <WechatIcon />,
      size: 48,
    },
  },
  qq: {
    id: "qq",
    text: "QQ",
    mobileIcon: {
      type: "svg",
      content: <QQIcon />,
      size: 40,
    },
    desktopIcon: {
      type: "svg",
      content: <QQIcon />,
      size: 48,
    },
  },
  sso: {
    id: "sso",
    text: "SSO",
    mobileIcon: {
      type: "svg",
      content: <SSOIcon />,
      size: 40,
    },
    desktopIcon: {
      type: "svg",
      content: <SSOIcon />,
      size: 48,
    },
  },
  apple: {
    id: "apple",
    text: "苹果",
    mobileIcon: {
      type: "svg",
      content: <AppleIcon />,
      size: 40,
    },
    desktopIcon: {
      type: "svg",
      content: <AppleIcon />,
      size: 48,
    },
  },
  more: {
    id: "more",
    text: "更多",
    mobileIcon: {
      type: "svg",
      content: <MoreIcon />,
      size: 40,
    },
    desktopIcon: {
      type: "svg",
      content: <MoreIcon />,
      size: 48,
    },
  },
};
