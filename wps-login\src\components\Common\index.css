.login {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.back_btn {
  background: none;
  border: none;
  color: #4285f4;
  font-size: 14px;
  cursor: pointer;
  padding: 0;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: color 0.2s;
  text-decoration: none;
  align-self: flex-start;
  position: relative;
  left: -8px;
}

.back_btn .icon {
  width: 16px;
  height: 16px;
}

.back_btn:hover {
  color: #1976d2;
  text-decoration: none;
}

.login_header {
  margin-bottom: 5px;
}

.login-child {
  width: 100%;
}

.nav_title {
  font-size: 22px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  text-align: center;
}

.nav_intro {
  font-size: 13px;
  color: #999;
  line-height: 1.4;
  text-align: center;
}

/* 移动端样式 - 小于640px */
@media (max-width: 640px) {
  .login {
    padding: 0;
  }

  .nav_title {
    font-size: 18px;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
  }

  .nav_title:empty {
    display: none;
    margin: 0;
  }

  .nav_intro {
    font-size: 14px;
    color: #666;
    margin-bottom: 24px;
    line-height: 1.4;
    text-align: center;
  }

  /* 当主标题为空但副标题存在时，调整副标题的上边距 */
  .nav_title:empty+.nav_intro {
    margin-top: 0;
  }

  .back_btn {
    font-size: 16px;
    margin-bottom: 20px;
    left: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #4285f4;
    padding: 8px 0;
  }

  .back_btn .icon {
    width: 20px;
    height: 20px;
  }

  .login_header {
    margin-bottom: 20px;
  }

  /* 当标题为空时，隐藏标题区域 */
  .login_header:empty {
    display: none;
    margin: 0;
  }

  .nav_title:empty {
    display: none;
  }

  .nav_intro:empty {
    display: none;
  }
}