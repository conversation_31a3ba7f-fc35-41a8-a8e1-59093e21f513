.icon-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  padding: 8px;
  border-radius: 8px;
}

.icon-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.icon-button:active {
  transform: translateY(0);
  background-color: rgba(0, 0, 0, 0.1);
}

.icon-button--disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.icon-button--disabled:hover {
  background-color: transparent;
  transform: none;
}

.icon-button__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
}

.icon-button__icon--text {
  font-weight: bold;
  color: #666;
}

.icon-button__text {
  font-size: 12px;
  color: #666;
  text-align: center;
  line-height: 1.2;
}

/* 移动端样式 */
@media (max-width: 640px) {
  .icon-button {
    padding: 6px;
    min-width: 60px;
  }

  .icon-button__text {
    font-size: 11px;
  }
}

/* PC端样式 */
@media (min-width: 481px) {
  .icon-button {
    padding: 10px;
    min-width: 80px;
  }

  .icon-button__text {
    font-size: 13px;
  }
}