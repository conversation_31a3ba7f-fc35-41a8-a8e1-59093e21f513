import { vi } from 'vitest';
import type { User } from '../../../type';

/**
 * 创建模拟用户数据
 */
export const createMockUser = (overrides: Partial<User> = {}): User => ({
  userid: 1,
  nickname: 'Test User',
  company_id: 1,
  company_name: 'Test Company',
  company_logo: '',
  company_custom_domain: '',
  is_company_account: false,
  avatar_url: '',
  status: 1,
  reason: '',
  reason_v2: { key: '', translate: false },
  link_text: '',
  link_text_v2: { key: '', translate: false },
  link_url: '',
  is_current: true,
  is_login: false,
  loginmode: 'phone',
  session_status: 1,
  logout_reason: '',
  need_tfa: false,
  default_select: true,
  last_active_time: Date.now(),
  ...overrides,
});

/**
 * 创建模拟 API 响应
 */
export const createMockApiResponse = {
  fetchStartVerify: (overrides = {}) => ({
    result: 'success',
    ssid: 'test-ssid-123',
    ...overrides,
  }),
  
  fetchLogin: (users: User[] = [createMockUser()]) => ({
    users,
  }),
};

/**
 * 模拟 API 错误
 */
export const createApiError = (message = 'API Error') => new Error(message);

/**
 * 测试用的手机号码
 */
export const TEST_PHONE_NUMBERS = {
  valid: '***********',
  invalid: '123',
  empty: '',
};

/**
 * 测试用的验证码
 */
export const TEST_VERIFICATION_CODES = {
  valid: '123456',
  invalid: '000',
  empty: '',
};

/**
 * 等待异步操作完成的辅助函数
 */
export const waitForAsync = (ms = 0) => 
  new Promise(resolve => setTimeout(resolve, ms));

/**
 * 模拟控制台方法，避免测试输出噪音
 */
export const mockConsole = () => {
  const originalConsole = { ...console };
  
  const mocks = {
    log: vi.spyOn(console, 'log').mockImplementation(() => {}),
    warn: vi.spyOn(console, 'warn').mockImplementation(() => {}),
    error: vi.spyOn(console, 'error').mockImplementation(() => {}),
  };
  
  const restore = () => {
    Object.keys(mocks).forEach(key => {
      mocks[key as keyof typeof mocks].mockRestore();
    });
  };
  
  return { mocks, restore };
};

/**
 * 创建模拟 props
 */
export const createMockProps = (overrides = {}) => ({
  onBack: vi.fn(),
  onLoginSuccess: vi.fn(),
  onCurrentBtn: vi.fn(),
  ...overrides,
});

/**
 * 常用的测试场景配置
 */
export const TEST_SCENARIOS = {
  // 成功登录场景
  successfulLogin: {
    phoneNumber: TEST_PHONE_NUMBERS.valid,
    verificationCode: TEST_VERIFICATION_CODES.valid,
    apiResponse: createMockApiResponse.fetchStartVerify(),
    loginResponse: createMockApiResponse.fetchLogin(),
  },
  
  // API 错误场景
  apiError: {
    phoneNumber: TEST_PHONE_NUMBERS.valid,
    verificationCode: TEST_VERIFICATION_CODES.valid,
    error: createApiError('Network Error'),
  },
  
  // 表单验证错误场景
  validationErrors: {
    emptyPhone: {
      phoneNumber: TEST_PHONE_NUMBERS.empty,
      verificationCode: TEST_VERIFICATION_CODES.valid,
      expectedError: '请输入手机号码',
    },
    emptyCode: {
      phoneNumber: TEST_PHONE_NUMBERS.valid,
      verificationCode: TEST_VERIFICATION_CODES.empty,
      expectedError: '请输入验证码',
    },
  },
};

/**
 * 模拟移动端/桌面端环境
 */
export const mockDeviceType = {
  mobile: () => vi.fn().mockReturnValue(true),
  desktop: () => vi.fn().mockReturnValue(false),
};

/**
 * 模拟定时器相关的辅助函数
 */
export const timerHelpers = {
  setup: () => vi.useFakeTimers(),
  cleanup: () => vi.useRealTimers(),
  advance: (ms: number) => vi.advanceTimersByTime(ms),
  advanceToNextTimer: () => vi.advanceTimersToNextTimer(),
  runAllTimers: () => vi.runAllTimers(),
};

/**
 * 常用的等待条件
 */
export const waitConditions = {
  verificationCodeInput: () => 'short信验证码',
  countdownText: (seconds: number) => new RegExp(`重发\\(${seconds}s\\)`),
  errorMessage: (message: string) => message,
  loadingState: () => '验证中...',
};

/**
 * 表单交互辅助函数
 */
export const formHelpers = {
  fillPhoneNumber: async (user: any, phoneNumber: string) => {
    const phoneInput = document.querySelector('input[placeholder="手机号码"]') as HTMLInputElement;
    if (phoneInput) {
      await user.clear(phoneInput);
      await user.type(phoneInput, phoneNumber);
    }
  },
  
  fillVerificationCode: async (user: any, code: string) => {
    const codeInput = document.querySelector('input[placeholder="短信验证码"]') as HTMLInputElement;
    if (codeInput) {
      await user.clear(codeInput);
      await user.type(codeInput, code);
    }
  },
  
  clickSmartVerify: async (user: any) => {
    const button = document.querySelector('button:contains("点击按钮开始智能验证")') as HTMLButtonElement;
    if (button) {
      await user.click(button);
    }
  },
  
  clickSendCode: async (user: any) => {
    const button = document.querySelector('button:contains("发送验证码")') as HTMLButtonElement;
    if (button) {
      await user.click(button);
    }
  },
  
  clickLogin: async (user: any) => {
    const button = document.querySelector('button:contains("立即登录/注册")') as HTMLButtonElement;
    if (button) {
      await user.click(button);
    }
  },
};
