.phone_login_section {
  padding: 15px 0;
}

.phone_input_group {
  display: flex;
  margin-bottom: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow: hidden;
  background: white;
  height: 48px;
}

.country_code_select {
  background: #f8f9fa;
  border-right: 1px solid #e0e0e0;
  position: relative;
  min-width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.country_code {
  background: transparent;
  border: none;
  padding: 0 8px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  outline: none;
  appearance: none;
  text-align: center;
  width: 100%;
}

.country_code_select::after {
  content: "▼";
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 10px;
  color: #999;
  pointer-events: none;
}

.send_code_count {
  color: #999 !important;
}

.phone_input {
  flex: 1;
  border: none;
  padding: 0 16px;
  font-size: 14px;
  outline: none;
  background: transparent;
  height: 100%;
}

.phone_input::placeholder {
  color: #999;
}

.phone_input:focus,
.phone_input_group:focus-within {
  border-color: #4285f4;
}

/* 错误提示样式 */
.error_message {
  color: #ff4444;
  font-size: 14px;
  text-align: left;
  line-height: 1.4;
}

.smart_verify_section {
  margin-bottom: 1px;
}

.verify_button_container {
  display: flex;
  justify-content: center;
}

.smart_verify_btn {
  display: flex;

  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
  color: #333;
  width: 100%;
  height: 48px;
}

.smart_verify_btn:hover {
  border-color: #4285f4;
  background: #f8f9ff;
}

.smart_verify_btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.verify_icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #00d4aa;
  border-radius: 50%;
  margin-right: 8px;
  position: relative;
  background: #00d4aa;
}

.verify_icon.scale {
  animation: scaleIcon 1s infinite alternate;

}

@keyframes scaleIcon {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.3);
    /* 放大1.5倍 */
  }

  100% {
    transform: scale(1);
  }
}


.verify_icon::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
}

.verify_text {
  color: #333;
  font-size: 14px;
}

.verification_group {
  display: flex;
  align-items: center;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  margin-bottom: 3px;
  height: 48px;
  background: white;
  overflow: hidden;
}

.verification_input {
  flex: 1;
  border: none;
  padding: 12px 16px;
  font-size: 14px;
  outline: none;
  background: transparent;
  height: 100%;
  box-sizing: border-box;
}

.verification_input::placeholder {
  color: #999;
}

.verification_group:focus-within {
  border-color: #4285f4;
}

.send_code_btn {
  background: transparent;
  color: #42a5e7;
  border: none;
  border-left: 1px solid #e0e0e0;
  padding: 0 16px;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.2s;
  min-width: 100px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send_code_btn:hover {
  background: #f0f8ff;
  color: #1976d2;
}

.login_submit_btn {
  width: 100%;
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 14px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  margin-bottom: 5px;
  transition: background-color 0.2s;
  height: 48px;
  margin-top: 10px;
}

.login_submit_btn:hover {
  background: #1976d2;
}


.checkbox-group {
  font-size: 14px;
  color: #333;
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 16px;
  margin-left: 25px;
}

.checkbox-div {
  display: flex;
  align-items: flex-start;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 6px;
  line-height: 1.5;
  cursor: pointer;
  font-size: 13px;
}

.checkbox-item input[type="checkbox"] {
  margin: 0;
  vertical-align: middle;
}

.protocol_link {
  color: #4285f4;
  text-decoration: none;
  margin: 0 2px;
}

.protocol_link:hover {
  text-decoration: none;
}

.or_divider {
  text-align: center;
  color: #999;
  font-size: 14px;
  margin: 20px 0;
}

.login_methods {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 10px;
}

.login_method {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
  padding: 8px;
  border-radius: 8px;
}

.method_icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-bottom: 6px;
  background-size: 20px 20px;
  background-repeat: no-repeat;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
}



.sso_icon {
  background: #867CFE;
}


.method_text {
  font-size: 12px;
  color: #666;
  text-align: center;
}

/* 移动端样式 - 小于640px */
@media (max-width: 640px) {
  .phone_login_section {
    padding: 20px 0 0;
    max-width: 100%;
  }

  .phone_input_group {
    margin-bottom: 16px;
    border-radius: 4px;
    height: 48px;
    border: 1px solid #e0e0e0;
    background: #fff;
  }

  .phone_input {
    font-size: 16px;
    padding: 0 12px;
    background: transparent;
  }

  .country_code_select {
    border-right: 1px solid #e0e0e0;
  }

  .country_code {
    font-size: 16px;
    padding: 0 12px;
    background: transparent;
  }

  .smart_verify_btn {
    height: 48px;
    border-radius: 4px;
    font-size: 16px;
    border: 1px solid #e0e0e0;
    margin-bottom: 16px;
    background: #fff;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0 12px;
    gap: 8px;
  }

  .verification_input {
    height: 48px;
    border-radius: 4px;
    font-size: 16px;
    padding: 0 12px;
    border: 1px solid #e0e0e0;
    background: #fff;
  }

  .send_code_btn {
    height: 48px;
    border-radius: 4px;
    font-size: 14px;
    padding: 0 16px;
  }

  .login_submit_btn {
    height: 48px;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 500;
    width: 100%;
    background: #4285f4;
    border: none;
    color: white;
  }

  .error_message {
    font-size: 14px;
    margin-top: 8px;
    color: #f44336;
  }

  .auto_login_section {

    display: flex !important;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
    min-height: 24px;
    width: 100%;
  }

  /* 移动端错误消息样式 */
  .auto_login_section .error_message {
    flex: 1;
    margin: 0 !important;
    margin-top: 0 !important;
    font-size: 12px;
    text-align: right;
    order: 2;
    color: #f44336;

  }

  .auto_login_section .checkbox-div {
    flex-shrink: 0;
    order: 1;
    margin: 0;

  }

  .protocol_section {
    margin-top: 16px;
    margin-bottom: 20px;
  }

  .login_options {
    margin-top: 24px;
  }

  .checkbox-group {
    margin-bottom: 20px;
  }

  .checkbox-div {
    margin-bottom: 12px;
  }


  .auto_login_section .checkbox-div {
    margin-bottom: 0 !important;
  }

  .checkbox-item {
    font-size: 14px;
    color: #666;
  }

  .checkbox-item input[type="checkbox"] {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }

  .protocol_link {
    color: #4285f4;
    text-decoration: none;
  }

  .or_divider {
    text-align: center;
    color: #999;
    font-size: 14px;
    margin: 24px 0;
    position: relative;
  }

  .or_divider::before,
  .or_divider::after {
    content: "";
    position: absolute;
    top: 50%;
    width: 40%;
    height: 1px;
    background: #e0e0e0;
  }

  .or_divider::before {
    left: 0;
  }

  .or_divider::after {
    right: 0;
  }

  .login_methods {
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
  }

  .login_method {
    padding: 12px;
    border-radius: 8px;
    transition: background-color 0.2s;
  }

  .login_method:hover {
    background-color: #f5f5f5;
  }

  .method_icon {
    width: 30px;
    height: 30px;
    margin-bottom: 8px;
  }

  .method_text {
    font-size: 12px;
    color: #666;
    margin-top: 6px;
  }
}