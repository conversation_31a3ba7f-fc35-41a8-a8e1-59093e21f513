import { useEffect, useState } from "react";
import Modal from "../../components/Modal";
import { type LoginMethod, type ViewType } from "../../constants";
import { useMobile } from "../../hooks/useMobile";
import type { User } from "../../type";
import ExCompany from "../ExCompany";
import PhoneLogin from "../PhoneLogin";
import QRCodeLogin from "../QRCodeLogin";
import SSOLogin from "../SSOLogin";
import UsersList from "../UsersList";
import "./index.css";

function Home() {
  const [showModal, setShowModal] = useState(false);
  const [hasAgreedProtocol, setHasAgreedProtocol] = useState(() => {
    // 从localStorage读取协议同意状态
    return localStorage.getItem("wps-protocol-agreed") === "true";
  });
  const [currentBtn, setCurrentBtn] = useState<LoginMethod | null>(null);
  const [currentView, setCurrentView] = useState<ViewType>("qrcode");
  const [users, setUsers] = useState<User[]>([]);
  // 使用自定义hook检测是否是移动端
  const isMobile = useMobile();

  // 初始化视图和监听窗口大小变化，根据移动端状态切换视图
  useEffect(() => {
    console.log("isMobile", isMobile);
    // 如果是移动端且当前是二维码登录，切换到手机登录
    if (isMobile && currentView === "qrcode") {
      setCurrentView("phone");
    }
    // 如果不是移动端且当前是手机登录，切换到二维码登录
    else if (!isMobile && currentView === "phone") {
      setCurrentView("qrcode");
    }
  }, [isMobile, currentView]);

  const handleCurrentBtn = (value: LoginMethod) => {
    if (hasAgreedProtocol) {
      // 如果已经同意过协议，直接跳转
      if (value === "qq") {
        alert("QQ登录功能开发中...");
        return;
      }
      console.log("handleCurrentBtn被调用", value);
      setCurrentView(value);
    } else {
      // 如果还没同意过协议，记录按钮并显示Modal
      setCurrentBtn(value);
      setShowModal(true);
    }
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setCurrentBtn(null);
  };

  //用户确认协议后，设置已同意标志并跳转到对应组件
  const handleConfirm = () => {
    setShowModal(false);
    setHasAgreedProtocol(true); // 标记用户已同意协议
    localStorage.setItem("wps-protocol-agreed", "true"); // 保存到localStorage
    if (currentBtn) {
      if (currentBtn === "qq") {
        alert("QQ登录功能开发中...");
      } else {
        setCurrentView(currentBtn);
      }
    }
    setCurrentBtn(null);
  };

  const handleBackToQRCode = () => {
    setCurrentView("qrcode");
  };

  const handleBackFromSSO = () => {
    console.log("handleBackFromSSO 被调用");
    console.log("当前窗口宽度:", window.innerWidth);
    console.log("isMobile 状态:", isMobile);
    console.log("当前视图:", currentView);

    if (isMobile) {
      console.log("这是移动端，切换到 phone");
      setCurrentView("phone");
    } else {
      console.log("这是桌面端，切换到 qrcode");
      setCurrentView("qrcode");
    }
  };

  const handleBackFromExCompany = () => {
    setCurrentView("qrcode");
  };

  const handleLoginSuccess = (usersData: User[]) => {
    setUsers(usersData);
    setCurrentView("userlist");
  };

  const handleBackFromUsersList = () => {
    setCurrentView("qrcode");
    setUsers([]);
  };

  return (
    <div className="wrap">
      <div className="left_section">
        <img
          src={
            isMobile
              ? "https://ac.wpscdn.cn/account/libs/img/v2/logo/mobile_logo_x2.1db52c7f.png"
              : "https://ac.wpscdn.cn/account/libs/img/v2/logo/logo_x2.89795d69.png"
          }
          alt="WPS Logo"
          className="logo_image"
        />
      </div>

      <div className="right_section">
        <div className="login_container">
          {currentView === "qrcode" && (
            <QRCodeLogin onCurrentBtn={handleCurrentBtn} />
          )}
          {currentView === "phone" && (
            <PhoneLogin
              onBack={handleBackToQRCode}
              onLoginSuccess={handleLoginSuccess}
              onCurrentBtn={handleCurrentBtn}
            />
          )}
          {currentView === "sso" && <SSOLogin onBack={handleBackFromSSO} />}

          {currentView === "more" && (
            <ExCompany onBack={handleBackFromExCompany} />
          )}

          {currentView === "userlist" && (
            <UsersList users={users} onBack={handleBackFromUsersList} />
          )}
        </div>
      </div>

      <Modal
        isOpen={showModal}
        onClose={handleCloseModal}
        onConfirm={handleConfirm}
      />
    </div>
  );
}

export default Home;
