/* 二维码区域 */
.qr_code_section {
  margin-bottom: 35px;
}

.qr_code_container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 200px;
}

.qr_code_placeholder {
  width: 160px;
  height: 160px;
  position: relative;
}

.checkbox-group {
  font-size: 14px;
  color: #333;
  display: flex;
  flex-direction: column;
  gap: 10px;
  /* 每个复选项的间距 */
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 6px;
  line-height: 1.5;
  cursor: pointer;
  font-size: 13px;
}

.checkbox-item input[type="checkbox"] {
  margin: 0;

  vertical-align: middle;
}

.checkbox-item a {
  color: #1677ff;
  text-decoration: none;
}

.checkbox-item a:hover {
  text-decoration: none;
}

.method_icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-bottom: 6px;
  background-size: 20px 20px;
  background-repeat: no-repeat;
  background-position: center;
}

.method_text {
  font-size: 11px;
  color: #666;
  text-align: center;
  font-weight: 400;
  white-space: nowrap;
}

.protocol_link {
  color: #4285f4;
  text-decoration: none;
  margin: 0 2px;
}

.protocol_link:hover {
  text-decoration: none;
}

.login_methods {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 10px;
}

.login_method {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
  padding: 8px;
  border-radius: 8px;
}

.sso_icon {
  background: #6d2fc5;
}

.method_text {
  font-size: 12px;
  color: #666;
  text-align: center;
}