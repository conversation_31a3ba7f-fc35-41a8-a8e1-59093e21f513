# Custom Hooks

## useMobile

自定义hook，用于检测当前设备是否为移动端。

### 使用方法

```tsx
import { useMobile } from '../hooks/useMobile';

function MyComponent() {
  const isMobile = useMobile();
  
  return (
    <div>
      {isMobile ? '移动端视图' : '桌面端视图'}
    </div>
  );
}
```

### 特性

- 自动检测窗口大小变化
- 基于 `MOBILE_BREAKPOINT` 常量判断移动端
- 返回布尔值，表示是否为移动端
- 自动清理事件监听器

### 配置

移动端判断阈值可以在 `src/constants/index.ts` 中修改 `MOBILE_BREAKPOINT` 常量。
