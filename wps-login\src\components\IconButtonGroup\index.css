.icon-button-group {
  width: 100%;
}

.icon-button-group__divider {
  text-align: center;
  margin: 20px 0;
  position: relative;
  color: #999;
  font-size: 14px;
}

.icon-button-group__divider::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #e0e0e0;
  z-index: 1;
}

.icon-button-group__divider::after {
  content: attr(data-text);
  background-color: white;
  padding: 0 16px;
  position: relative;
  z-index: 2;
}

.icon-button-group__container {
  display: grid;
  gap: 16px;
  justify-items: center;
  align-items: center;
}

.icon-button-group__item {
  width: 100%;
  max-width: 80px;
}

.sso_icon {
  background: #867CFE;
  border-radius: 50%;
}

/* 移动端样式 */
@media (max-width: 640px) {
  .icon-button-group__divider {
    margin: 16px 0;
    font-size: 13px;
  }

  .icon-button-group__container {
    gap: 12px;
  }

  .icon-button-group__item {
    max-width: 60px;
  }
}

/* PC端样式 */
@media (min-width: 481px) {
  .icon-button-group__divider {
    margin: 24px 0;
    font-size: 15px;
  }

  .icon-button-group__container {
    gap: 20px;
  }

  .icon-button-group__item {
    max-width: 100px;
  }
}