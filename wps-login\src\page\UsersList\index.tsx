import React, { useState } from "react";
import Common from "../../components/Common";
import UserItem from "../../components/UserItem";
import type { User } from "../../type";
import "./index.css";

interface UsersListProps {
  users: User[];
  onBack: () => void;
}

const UsersList: React.FC<UsersListProps> = ({ users }) => {
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);

  const handleUserSelect = (user: User, isSelected: boolean) => {
    if (isSelected) {
      setSelectedUsers((prev) => [...prev, user]);
    } else {
      setSelectedUsers((prev) =>
        prev.filter((u) => u.nickname !== user.nickname)
      );
    }
  };

  const handleLogin = () => {
    if (selectedUsers.length === 0) {
      alert("请至少选择一个账号");
      return;
    }

    // 显示选中的用户信息
    const userInfo = selectedUsers
      .map(
        (user) =>
          `昵称: ${user.nickname}\n公司: ${user.company_name}\n企业账号: ${
            user.is_company_account ? "是" : "否"
          }`
      )
      .join("\n\n");

    alert(`选中的用户信息:\n\n${userInfo}`);
  };

  // 生成选择状态提示文本
  const getSelectionText = () => {
    if (selectedUsers.length === 0) {
      return "";
    } else if (selectedUsers.length === 1) {
      return selectedUsers[0].nickname;
    } else {
      return `已选${selectedUsers.length}个账号，登录后可以切换访问${selectedUsers.length}个账号数据`;
    }
  };
  return (
    <Common
      headerClassName="user_login_title"
      title="当前手机号绑定了以下账号，请选择账号登录"
      isShowBack={false}
      fchildren={
        <div>
          <div className="user_login">
            <button onClick={handleLogin}>确认登录</button>
          </div>
        </div>
      }
    >
      <div className="users-list">
        {users.map((acc, idx) => (
          <UserItem
            key={idx}
            avatarUrl={acc.avatar_url}
            nickname={acc.nickname}
            companyName={acc.company_name}
            isCompanyAccount={acc.is_company_account}
            isCurrent={acc.is_current}
            isLogin={acc.is_login}
            onSelect={(isSelected) => handleUserSelect(acc, isSelected)}
          />
        ))}
      </div>
      {selectedUsers.length > 0 && (
        <div className="selection-status">{getSelectionText()}</div>
      )}
    </Common>
  );
};

export default UsersList;
