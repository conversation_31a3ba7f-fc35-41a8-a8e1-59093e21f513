# PhoneLogin 组件测试

这个目录包含了 PhoneLogin 组件的全面测试套件，使用 Vitest 和 React Testing Library。

## 测试文件结构

```
__tests__/
├── PhoneLogin.test.tsx           # 主要单元测试
├── PhoneLogin.integration.test.tsx  # 集成测试
├── PhoneLogin.api.test.tsx       # API 集成测试
├── test-utils.ts                 # 测试工具函数
└── README.md                     # 测试说明文档
```

## 测试覆盖范围

### 1. 基础功能测试 (`PhoneLogin.test.tsx`)
- ✅ 组件初始渲染
- ✅ 移动端/桌面端适配
- ✅ 手机号码输入
- ✅ 智能验证流程
- ✅ 验证码输入和发送
- ✅ 表单验证
- ✅ 登录流程
- ✅ 回调函数调用
- ✅ 错误处理

### 2. 集成测试 (`PhoneLogin.integration.test.tsx`)
- ✅ 完整登录流程
- ✅ 网络错误处理
- ✅ 定时器和状态管理
- ✅ 响应式行为
- ✅ 错误恢复
- ✅ 无障碍访问

### 3. API 集成测试 (`PhoneLogin.api.test.tsx`)
- ✅ fetchStartVerify API 调用
- ✅ fetchLogin API 调用
- ✅ API 错误处理
- ✅ API 调用顺序
- ✅ 不同响应格式处理

## 运行测试

### 安装依赖
```bash
npm install
```

### 运行所有测试
```bash
npm run test
```

### 运行特定测试文件
```bash
# 运行主要单元测试
npm run test PhoneLogin.test.tsx

# 运行集成测试
npm run test PhoneLogin.integration.test.tsx

# 运行 API 测试
npm run test PhoneLogin.api.test.tsx
```

### 运行测试并查看覆盖率
```bash
npm run test:coverage
```

### 运行测试 UI
```bash
npm run test:ui
```

## 测试场景

### 用户行为测试
1. **正常登录流程**
   - 点击智能验证按钮
   - 输入手机号码
   - 发送验证码
   - 输入验证码
   - 点击登录按钮
   - 验证成功回调

2. **表单验证**
   - 空手机号码验证
   - 空验证码验证
   - 错误格式验证

3. **错误处理**
   - 网络错误
   - API 错误
   - 验证失败

4. **移动端特性**
   - 响应式布局
   - 替代登录方式
   - 触摸交互

### API 集成测试
1. **API 调用验证**
   - 参数正确性
   - 调用顺序
   - 错误处理

2. **响应处理**
   - 成功响应
   - 错误响应
   - 空响应

## 测试工具

### `test-utils.ts` 提供的工具函数
- `createMockUser()` - 创建模拟用户数据
- `createMockApiResponse` - 创建模拟 API 响应
- `createApiError()` - 创建 API 错误
- `TEST_SCENARIOS` - 预定义测试场景
- `mockConsole()` - 模拟控制台输出
- `timerHelpers` - 定时器测试辅助函数

### Mock 策略
- **API 函数**: 使用 `vi.mock()` 模拟 API 调用
- **Hooks**: 模拟 `useMobile` hook
- **定时器**: 使用 `vi.useFakeTimers()` 控制时间
- **控制台**: 模拟控制台输出避免测试噪音

## 最佳实践

### 1. 测试隔离
- 每个测试用例都是独立的
- 使用 `beforeEach` 和 `afterEach` 清理状态
- Mock 函数在每个测试前重置

### 2. 用户行为模拟
- 使用 `@testing-library/user-event` 模拟真实用户交互
- 测试键盘导航和无障碍访问
- 验证视觉反馈和状态变化

### 3. 异步处理
- 使用 `waitFor` 等待异步操作
- 正确处理 Promise 和定时器
- 测试加载状态和错误状态

### 4. 边界情况
- 测试空值和无效输入
- 测试网络错误和 API 失败
- 测试极端用户行为

## 调试测试

### 查看测试输出
```bash
# 详细输出
npm run test -- --reporter=verbose

# 监听模式
npm run test -- --watch
```

### 调试特定测试
```bash
# 只运行匹配的测试
npm run test -- --grep "should successfully login"

# 运行单个测试文件
npm run test PhoneLogin.test.tsx
```

### 查看覆盖率报告
测试覆盖率报告会生成在 `coverage/` 目录中，可以在浏览器中打开 `coverage/index.html` 查看详细报告。

## 贡献指南

### 添加新测试
1. 确定测试类型（单元/集成/API）
2. 选择合适的测试文件
3. 使用现有的测试工具函数
4. 遵循现有的测试模式
5. 添加适当的文档注释

### 测试命名规范
- 使用描述性的测试名称
- 使用 "should" 开头描述期望行为
- 按功能分组测试用例

### Mock 使用原则
- 只 mock 外部依赖
- 保持 mock 简单和可预测
- 在测试中验证 mock 调用
